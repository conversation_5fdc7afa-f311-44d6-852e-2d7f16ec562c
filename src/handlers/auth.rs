use actix_web::{web, HttpResponse};
use serde_json::json;
use tracing::{info, warn, error};

use crate::services::{AuthService, EmailVerificationService};
use crate::models::{CreateUserRequest, LoginRequest};
use crate::models::email_verification::SendVerificationEmailRequest;
use crate::utils::errors::AppError;

pub fn configure_auth_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/login", web::post().to(login))
       .route("/register", web::post().to(register))
       .route("/logout", web::post().to(logout))
       .route("/verify", web::post().to(verify_token));
}

/// User registration endpoint
/// POST /auth/register
/// User registration endpoint
/// POST /auth/register
/// 
/// # Arguments
/// * `auth_service` - Authentication service instance
/// * `email_verification_service` - Email verification service instance
/// * `req_body` - JSON payload containing user registration details
/// 
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with registration status or error details
pub async fn register(
    crabshield: web::Data<AuthService>,
    email_verification_service: web::Data<EmailVerificationService>,
    req_body: web::Json<CreateUserRequest>,
) -> Result<HttpResponse, AppError> {
    info!("User registration request for email: {}", req_body.email);

    // Extract request data before any await points to avoid holding the request across await
    let user_data = req_body.into_inner();
    let email = user_data.email.clone();
    
    match crabshield.create_user(user_data).await { // Using crabshield instead of auth_service
        Ok(user) => {
            info!("User created successfully: {}", user.id);

            // Send verification email
            let verification_request = SendVerificationEmailRequest {
                email: user.email.clone(),
                resend: Some(false),
            };

            match email_verification_service.send_verification_email(verification_request).await {
                Ok(_) => {
                    info!("Verification email sent to: {}", email);
                    Ok(HttpResponse::Created().json(json!({
                        "success": true,
                        "message": "User registered successfully. Please check your email to verify your account.",
                        "user_id": user.id,
                        "email_verification_sent": true
                    })))
                }
                Err(e) => {
                    warn!("Failed to send verification email: {}", e);
                    Ok(HttpResponse::Created().json(json!({
                        "success": true,
                        "message": "User registered successfully, but verification email could not be sent. Please request a new verification email.",
                        "user_id": user.id,
                        "email_verification_sent": false
                    })))
                }
            }
        }
        Err(e) => {
            error!("User registration failed: {}", e);
            match e {
        AppError::BadRequest(msg) => Err(AppError::BadRequest(msg)),
        AppError::Conflict(msg) => Err(AppError::Conflict(msg)),
        _ => Err(AppError::InternalServerError("Registration failed".to_string()))
    }
        }
    }
}

/// User login endpoint
/// POST /auth/login
/// 
/// # Arguments
/// * `auth_service` - Authentication service instance
/// * `req_body` - JSON payload containing login credentials
/// 
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with authentication tokens or error details
pub async fn login(
    auth_service: web::Data<AuthService>,
    req: web::Json<LoginRequest>,
) -> Result<HttpResponse, AppError> {
    info!("Login attempt for email: {}", req.email);
    
    // Get client IP from request
    let ip_addr = "0.0.0.0".to_string(); // TODO: Get real IP from request
    let login_req = req.into_inner();
    let mfa_code = login_req.mfa_code.clone();

    // Authenticate the user
    let user = match auth_service.authenticate_user(login_req, &ip_addr).await? {
        Some(user) => user,
        None => return Err(AppError::Unauthorized("Invalid email or password".to_string())),
    };

    // Check if MFA is required
    if user.mfa_enabled {
        // If MFA is enabled but no code was provided
        if mfa_code.is_none() {
            return Ok(HttpResponse::Ok().json(json!({
                "message": "MFA required",
                "requires_mfa": true,
                "mfa_methods": ["totp"],
                "mfa_token": "mfa-token-placeholder" // TODO: Implement MFA token generation
            })));
        }

        // TODO: Verify MFA code
        // If we get here, MFA code was provided and needs to be verified
        if !auth_service.verify_mfa_code(&user, mfa_code.as_ref().unwrap()).await? {
            return Err(AppError::Unauthorized("Invalid MFA code".to_string()));
        }
    }

    // Generate tokens
    let tokens = auth_service.generate_tokens(&user, login_req.remember_me.unwrap_or(false))
        .await?;

    // Update last login
    auth_service.update_last_login(user.id, &ip_addr).await?;

    // Return success response with tokens
    Ok(HttpResponse::Ok().json(json!({
        "message": "Login successful",
        "access_token": tokens.access_token,
        "refresh_token": tokens.refresh_token,
        "expires_in": tokens.expires_in,
        "token_type": "Bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email_verified": user.email_verified_at.is_some(),
            "mfa_enabled": user.mfa_enabled
        }
    })))
}

/// User logout endpoint
/// POST /auth/logout
/// 
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response confirming logout
pub async fn logout() -> Result<HttpResponse, AppError> {
    // TODO: Implement logout logic
    // 1. Invalidate the current session token
    // 2. Clear any session cookies
    // 3. Log the logout event
    
    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Logged out successfully"
    })))
}

/// Token verification endpoint
/// POST /auth/verify
/// 
/// # Returns
/// * `Result<HttpResponse, AppError>` - JSON response with token verification status
pub async fn verify_token() -> Result<HttpResponse, AppError> {
    // TODO: Implement token verification
    Ok(HttpResponse::Ok().json(json!({
        "valid": true,
        "message": "Token is valid"
    })))
}
