use sqlx::{PgPool, postgres::PgPoolOptions};
use anyhow::Result;

/// Creates a new connection pool to the `PostgreSQL` database.
///
/// # Arguments
/// * `database_url` - The connection string for the `PostgreSQL` database
///
/// # Errors
/// Returns `Err` if:
/// - The database connection fails
/// - The connection pool cannot be created
pub async fn create_pool(database_url: &str) -> Result<PgPool> {
    let pool = PgPoolOptions::new()
        .max_connections(10)
        .connect(database_url)
        .await?;

    Ok(pool)
}

/// Applies database migrations to the connected database.
///
/// # Arguments
/// * `pool` - A reference to the database connection pool
///
/// # Errors
/// Returns `Err` if:
/// - The migration directory cannot be read
/// - Any migration fails to execute
/// - There's a database connection error
pub async fn run_migrations(pool: &PgPool) -> Result<()> {
    sqlx::migrate!("./migrations").run(pool).await?;
    Ok(())
}
