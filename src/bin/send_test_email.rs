use crabshield::{EmailService, EmailConfig, SmtpConfig};
use std::env;
use dotenvy::dotenv;

#[tokio::main]
async fn main() {
    dotenv().ok();

    // Fill out all config fields from env or hardcoded for testing
    let config = EmailConfig {
        provider: "resend".to_string(),
        resend_api_key: env::var("RESEND_API_KEY").unwrap(),
        from_address: env::var("EMAIL_FROM_ADDRESS").unwrap(),
        from_name: env::var("EMAIL_FROM_NAME").unwrap(),
        reply_to: env::var("EMAIL_REPLY_TO").unwrap(),
        smtp: SmtpConfig {
            host: env::var("SMTP_HOST").unwrap(),
            port: env::var("SMTP_PORT").unwrap().parse().unwrap(),
            user: env::var("SMTP_USER").unwrap(),
            password: env::var("SMTP_PASSWORD").unwrap(),
        },
    };

    // Use the constructor, not manual struct fields!
    let service = EmailService::new(config).expect("Failed to create EmailService");

    // Send test email
    let to_email = "<EMAIL>";
    let subject = "CrabShield Test Email";
    let html_body = "<strong>This is a test email from CrabShield!</strong>";
    let text_body = Some("This is a test email from CrabShield!");

    match service.send_email(to_email, subject, html_body, text_body).await {
        Ok(_) => println!("Email sent!"),
        Err(e) => eprintln!("Failed to send: {:?}", e),
    }
}