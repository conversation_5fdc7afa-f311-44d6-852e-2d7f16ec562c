use crate::models::{User, CreateUserRequest, LoginRequest, LoginAttemptRequest};
use crate::services::{PasswordService, LockoutService};
use crate::utils::errors::AppError;
use anyhow::Result;
use serde::{Serialize, Deserialize};
use sqlx::PgPool;
use uuid::Uuid;
use chrono::Utc;
use tracing::{info, warn, error};

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthTokens {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MfaVerification {
    pub verified: bool,
    pub recovery_codes: Option<Vec<String>>,
}

#[derive(Clone)]
pub struct AuthService {
    pool: PgPool,
    password_service: PasswordService,
    lockout_service: LockoutService,
}

impl AuthService {
    pub fn new(pool: PgPool, password_service: PasswordService, lockout_service: LockoutService) -> Self {
        Self {
            pool,
            password_service,
            lockout_service,
        }
    }

    /// Create a new user account
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<User, AppError> {
        info!("Creating new user with email: {}", request.email);

        // Check if user already exists
        if self.user_exists_by_email(&request.email).await? {
            warn!("Attempted to create user with existing email: {}", request.email);
            return Err(AppError::BadRequest("User with this email already exists".to_string()));
        }

        // Check if username already exists
        if self.user_exists_by_username(&request.username).await? {
            warn!("Attempted to create user with existing username: {}", request.username);
            return Err(AppError::BadRequest("User with this username already exists".to_string()));
        }

        // Validate password strength
        let password_analysis = self.password_service.analyze_password_strength(&request.password);
        if password_analysis.score < 60 {
            let mut issues = Vec::new();
            if !password_analysis.has_lowercase { issues.push("missing lowercase letters"); }
            if !password_analysis.has_uppercase { issues.push("missing uppercase letters"); }
            if !password_analysis.has_numbers { issues.push("missing numbers"); }
            if !password_analysis.has_symbols { issues.push("missing symbols"); }
            if password_analysis.is_common { issues.push("password is too common"); }
            if password_analysis.length < 8 { issues.push("password is too short"); }

            return Err(AppError::BadRequest(format!(
                "Password is too weak. Score: {}/100. Issues: {}",
                password_analysis.score,
                if issues.is_empty() { "low entropy".to_string() } else { issues.join(", ") }
            )));
        }

        // Hash the password
        let password_hash = self.password_service.hash_password(&request.password)
            .map_err(|e| {
                error!("Failed to hash password: {}", e);
                AppError::InternalServerError("Failed to process password".to_string())
            })?;

        // Create user in database
        let user_id = Uuid::new_v4();
        let now = Utc::now();

        let query = r"
            INSERT INTO users (
                id, email, username, password_hash, first_name, last_name,
                is_active, is_verified, email_verified_at, failed_login_attempts,
                locked_until, last_login_at, last_login_ip, password_changed_at,
                must_change_password, created_at, updated_at, created_by, updated_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
            ) RETURNING *
        ";

        let user = sqlx::query_as::<_, User>(query)
            .bind(user_id)
            .bind(&request.email)
            .bind(&request.username)
            .bind(&password_hash)
            .bind(&request.first_name)
            .bind(&request.last_name)
            .bind(true) // is_active
            .bind(false) // is_verified
            .bind(None::<chrono::DateTime<Utc>>) // email_verified_at
            .bind(0) // failed_login_attempts
            .bind(None::<chrono::DateTime<Utc>>) // locked_until
            .bind(None::<chrono::DateTime<Utc>>) // last_login_at
            .bind(None::<String>) // last_login_ip
            .bind(now) // password_changed_at
            .bind(false) // must_change_password
            .bind(now) // created_at
            .bind(now) // updated_at
            .bind(None::<Uuid>) // created_by
            .bind(None::<Uuid>) // updated_by
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to create user in database: {}", e);
                AppError::InternalServerError("Failed to create user".to_string())
            })?;

        info!("Successfully created user with ID: {}", user.id);
        Ok(user)
    }

    /// Authenticate a user with email/username and password
    pub async fn authenticate_user(&self, request: LoginRequest, ip_address: &str) -> Result<Option<User>, AppError> {
        info!("Authentication attempt for email: {}", request.email);

        // Check IP rate limiting first
        let ip_rate_limit = self.lockout_service.check_ip_rate_limit(ip_address).await
            .map_err(|e| {
                error!("Failed to check IP rate limit: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if ip_rate_limit.is_rate_limited {
            warn!("IP {} is rate limited", ip_address);
            self.log_failed_attempt(&request.email, None, ip_address, "IP rate limited", false).await?;
            return Err(AppError::Unauthorized("Too many requests from this IP address".to_string()));
        }

        // Get user by email
        let user = match self.get_user_by_email(&request.email).await? {
            Some(user) => user,
            None => {
                warn!("Authentication failed: user not found for email {}", request.email);
                self.log_failed_attempt(&request.email, None, ip_address, "User not found", false).await?;
                return Err(AppError::Unauthorized("Invalid credentials".to_string()));
            }
        };

        // Check if user is active
        if !user.is_active {
            warn!("Authentication failed: user {} is inactive", user.id);
            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "User inactive", false).await?;
            return Err(AppError::Unauthorized("Account is disabled".to_string()));
        }

        // Check account lockout
        let lockout_info = self.lockout_service.check_account_lockout(user.id).await
            .map_err(|e| {
                error!("Failed to check account lockout: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if lockout_info.is_locked {
            warn!("Authentication failed: user {} is locked until {:?}", user.id, lockout_info.locked_until);
            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "Account locked", true).await?;
            return Err(AppError::Unauthorized(format!(
                "Account is locked until {}",
                lockout_info.locked_until
                    .map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
                    .unwrap_or_else(|| "unknown".to_string())
            )));
        }

        // Verify password
        let password_valid = self.password_service.verify_password(&request.password, &user.password_hash)
            .map_err(|e| {
                error!("Failed to verify password: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

        if !password_valid {
            warn!("Authentication failed: invalid password for user {}", user.id);

            // Record failed attempt and check if account should be locked
            let lockout_result = self.lockout_service.record_failed_attempt(
                user.id,
                ip_address,
                "Invalid password"
            ).await.map_err(|e| {
                error!("Failed to record failed attempt: {}", e);
                AppError::InternalServerError("Authentication service unavailable".to_string())
            })?;

            self.log_failed_attempt(&request.email, Some(user.id), ip_address, "Invalid password", lockout_result.is_locked).await?;

            if lockout_result.is_locked {
                return Err(AppError::Unauthorized(format!(
                    "Account locked due to too many failed attempts. Locked until {}",
                    lockout_result.locked_until
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S UTC").to_string())
                        .unwrap_or_else(|| "unknown".to_string())
                )));
            }

            return Err(AppError::Unauthorized("Invalid credentials".to_string()));
        }

        // Authentication successful - clear any failed attempts
        if let Err(e) = self.lockout_service.record_successful_attempt(user.id).await {
            error!("Failed to record successful attempt: {}", e);
            // Don't fail authentication for this, just log the error
        }

        // Update last login information
        let updated_user = self.update_last_login(user.id, ip_address).await?;

        info!("Authentication successful for user {}", user.id);
        Ok(Some(updated_user))
    }

    /// Get user by ID
    pub async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<User>, AppError> {
        let query = "SELECT * FROM users WHERE id = $1 AND is_active = true";

        let user = sqlx::query_as::<_, User>(query)
            .bind(user_id)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to get user by ID: {}", e);
                AppError::InternalServerError("Failed to retrieve user".to_string())
            })?;

        Ok(user)
    }

    /// Get user by email
    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>, AppError> {
        let query = "SELECT * FROM users WHERE email = $1";

        let user = sqlx::query_as::<_, User>(query)
            .bind(email)
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to get user by email: {}", e);
                AppError::InternalServerError("Failed to retrieve user".to_string())
            })?;

        Ok(user)
    }

    /// Check if user exists by email
    async fn user_exists_by_email(&self, email: &str) -> Result<bool, AppError> {
        let query = "SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)";

        let exists: (bool,) = sqlx::query_as(query)
            .bind(email)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to check if user exists by email: {}", e);
                AppError::InternalServerError("Database error".to_string())
            })?;

        Ok(exists.0)
    }

    /// Check if user exists by username
    async fn user_exists_by_username(&self, username: &str) -> Result<bool, AppError> {
        let query = "SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)";

        let exists: (bool,) = sqlx::query_as(query)
            .bind(username)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to check if user exists by username: {}", e);
                AppError::InternalServerError("Database error".to_string())
            })?;

        Ok(exists.0)
    }

    /// Update last login information and reset failed login attempts
    pub async fn update_last_login(&self, user_id: Uuid, ip_address: &str) -> Result<User, AppError> {
        let now = Utc::now();
        let query = r"
            UPDATE users
            SET last_login_at = $1, 
                last_login_ip = $2, 
                updated_at = $1,
                failed_login_attempts = 0,  // Reset failed attempts on successful login
                locked_until = NULL          // Clear any lock
            WHERE id = $3
            RETURNING *
        ";

        let user = sqlx::query_as::<_, User>(query)
            .bind(now)
            .bind(ip_address)
            .bind(user_id)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to update last login for user {}: {}", user_id, e);
                AppError::InternalServerError("Failed to update last login".to_string())
            })?;

        info!("Updated last login for user {} from IP {}", user_id, ip_address);
        Ok(user)
    }

    /// Log a failed authentication attempt
    async fn log_failed_attempt(
        &self,
        email: &str,
        user_id: Option<Uuid>,
        ip_address: &str,
        failure_reason: &str,
        blocked_by_rate_limit: bool,
    ) -> Result<(), AppError> {
        let attempt_request = LoginAttemptRequest {
            email: Some(email.to_string()),
            user_id,
            ip_address: ip_address.to_string(),
            user_agent: None, // This would come from the HTTP request
            success: false,
            failure_reason: Some(failure_reason.to_string()),
            mfa_required: false,
            mfa_success: None,
            suspicious_activity: false,
            blocked_by_rate_limit,
            country_code: None, // Could be added with IP geolocation
            duration_ms: None,
        };

        let query = r"
            INSERT INTO login_attempts (
                id, email, user_id, ip_address, user_agent, success, failure_reason,
                mfa_required, mfa_success, suspicious_activity, blocked_by_rate_limit,
                country_code, attempted_at, duration_ms
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ";

        sqlx::query(query)
            .bind(Uuid::new_v4())
            .bind(&attempt_request.email)
            .bind(&attempt_request.user_id)
            .bind(&attempt_request.ip_address)
            .bind(&attempt_request.user_agent)
            .bind(attempt_request.success)
            .bind(&attempt_request.failure_reason)
            .bind(attempt_request.mfa_required)
            .bind(&attempt_request.mfa_success)
            .bind(attempt_request.suspicious_activity)
            .bind(attempt_request.blocked_by_rate_limit)
            .bind(&attempt_request.country_code)
            .bind(Utc::now())
            .bind(&attempt_request.duration_ms)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to log failed attempt: {}", e);
                // Don't fail the authentication flow for logging errors
                AppError::InternalServerError("Logging error".to_string())
            })?;

        Ok(())
    }

    /// Change user password
    pub async fn change_password(
        &self,
        user_id: Uuid,
        current_password: &str,
        new_password: &str,
    ) -> Result<(), AppError> {
        // Get current user
        let user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

        // Verify current password
        let password_valid = self.password_service.verify_password(current_password, &user.password_hash)
            .map_err(|e| {
                error!("Failed to verify current password: {}", e);
                AppError::InternalServerError("Password verification failed".to_string())
            })?;

        if !password_valid {
            return Err(AppError::Unauthorized("Current password is incorrect".to_string()));
        }

        // Validate new password strength
        let password_analysis = self.password_service.analyze_password_strength(new_password);
        if password_analysis.score < 60 {
            let mut issues = Vec::new();
            if !password_analysis.has_lowercase { issues.push("missing lowercase letters"); }
            if !password_analysis.has_uppercase { issues.push("missing uppercase letters"); }
            if !password_analysis.has_numbers { issues.push("missing numbers"); }
            if !password_analysis.has_symbols { issues.push("missing symbols"); }
            if password_analysis.is_common { issues.push("password is too common"); }
            if password_analysis.length < 8 { issues.push("password is too short"); }

            return Err(AppError::BadRequest(format!(
                "New password is too weak. Score: {}/100. Issues: {}",
                password_analysis.score,
                if issues.is_empty() { "low entropy".to_string() } else { issues.join(", ") }
            )));
        }

        // Hash new password
        let new_password_hash = self.password_service.hash_password(new_password)
            .map_err(|e| {
                error!("Failed to hash new password: {}", e);
                AppError::InternalServerError("Failed to process new password".to_string())
            })?;

        // Update password in database
        let now = Utc::now();
        let query = r"
            UPDATE users
            SET password_hash = $1, password_changed_at = $2, updated_at = $2, must_change_password = false
            WHERE id = $3
        ";

        sqlx::query(query)
            .bind(&new_password_hash)
            .bind(now)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to update password: {}", e);
                AppError::InternalServerError("Failed to update password".to_string())
            })?;

        info!("Password changed successfully for user {}", user_id);
        Ok(())
    }

    /// Deactivate user account
    pub async fn deactivate_user(&self, user_id: Uuid, admin_user_id: Uuid) -> Result<(), AppError> {
        let query = r"
            UPDATE users
            SET is_active = false, updated_at = $1, updated_by = $2
            WHERE id = $3
        ";

        sqlx::query(query)
            .bind(Utc::now())
            .bind(admin_user_id)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                error!("Failed to deactivate user: {}", e);
                AppError::InternalServerError("Failed to deactivate user".to_string())
            })?;

        info!("User {} deactivated by admin {}", user_id, admin_user_id);
        Ok(())
    }

    /// Activate user account
    pub async fn activate_user(&self, user_id: Uuid, admin_user_id: Uuid) -> Result<(), AppError> {
        let now = Utc::now();
        sqlx::query!(
            "UPDATE users SET is_active = true, updated_at = $1, updated_by = $2 WHERE id = $3",
            now,
            admin_user_id,
            user_id
        )
        .execute(&self.pool)
        .await?;

        info!("User {} activated by admin {}", user_id, admin_user_id);
        Ok(())
    }

    /// Verify MFA code for a user using TOTP
    pub async fn verify_mfa_code(&self, user: &User, code: &str) -> Result<bool, AppError> {
        // In a real implementation, you would:
        // 1. Get the user's MFA secret from the database
        // 2. Verify the TOTP code against the secret
        // 3. Check if the code has been used before (replay attack protection)
        // 4. Return true if verification succeeds, false otherwise
        
        // For now, we'll implement a simple verification that accepts any 6-digit code
        // as valid for testing purposes
        
        // Check if the code is a 6-digit number
        if code.len() != 6 || !code.chars().all(|c| c.is_ascii_digit()) {
            return Ok(false);
        }
        
        // TODO: Replace with actual TOTP verification
        // let secret = self.get_mfa_secret(user.id).await?;
        // let verified = totp_rs::verify_totp(&secret, code, 30, 1)?; // 30s window, 1 step tolerance
        
        // For now, just log the verification attempt and return true
        info!("MFA verification for user {} with code {} (real verification not implemented yet)", user.id, code);
        
        Ok(true)
    }

    /// Generate JWT access and refresh tokens for a user
    pub async fn generate_tokens(&self, user: &User, remember_me: bool) -> Result<AuthTokens, AppError> {
        use jsonwebtoken::{encode, EncodingKey, Header};
        use serde::{Deserialize, Serialize};
        use std::time::{SystemTime, UNIX_EPOCH};
        
        #[derive(Debug, Serialize, Deserialize)]
        struct Claims {
            sub: String, // Subject (user id)
            exp: usize,  // Expiration time
            iat: usize,  // Issued at
            email: String,
        }

        // Get current timestamp
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AppError::InternalServerError("System time is before UNIX_EPOCH".to_string()))?;
        
        let now_secs = now.as_secs() as usize;
        
        // Set token expiration (1 day by default, 30 days if remember_me is true)
        let expires_in = if remember_me { 60 * 60 * 24 * 30 } else { 60 * 60 * 24 }; // 30 days or 1 day
        let exp = now_secs + expires_in;
        
        // Create claims for access token
        let claims = Claims {
            sub: user.id.to_string(),
            exp,
            iat: now_secs,
            email: user.email.clone(),
        };
        
        // In a real implementation, you would:
        // 1. Use a secure secret key from configuration
        // 2. Store the refresh token in the database
        // 3. Add more claims as needed (roles, permissions, etc.)
        // 4. Handle token refresh logic
        
        // For now, we'll use a placeholder secret
        let secret = b"your-256-bit-secret"; // TODO: Move to configuration
        
        // Generate access token
        let access_token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret),
        ).map_err(|e| {
            error!("Failed to generate access token: {}", e);
            AppError::InternalServerError("Failed to generate access token".to_string())
        })?;
        
        // Generate refresh token (in a real implementation, this would be a different JWT with a longer expiry)
        let refresh_token = encode(
            &Header::default(),
            &Claims {
                sub: user.id.to_string(),
                exp: now_secs + (60 * 60 * 24 * 90), // 90 days
                iat: now_secs,
                email: user.email.clone(),
            },
            &EncodingKey::from_secret(secret),
        ).map_err(|e| {
            error!("Failed to generate refresh token: {}", e);
            AppError::InternalServerError("Failed to generate refresh token".to_string())
        })?;
        
        // TODO: Store refresh token in the database with user ID and expiry
        
        Ok(AuthTokens {
            access_token,
            refresh_token,
            expires_in: expires_in as i64, // Cast to i64 for JSON serialization
        })
    }

    /// Generate MFA token for a user
    pub fn generate_mfa_token(&self, user: &User) -> String {
        // Generate a secure random token with user-specific data
        let token_data = format!(
            "{}:{}:{}",
            user.id,
            Utc::now().timestamp(),
            Uuid::new_v4()
        );
        
        // Hash the token data for security
        let hash = self.password_service.hash_password(&token_data)
            .unwrap_or_else(|_| "mfa-token-fallback".to_string());
            
        // Return a URL-safe base64 encoded version of the hash
        base64::encode_config(hash.as_bytes(), base64::URL_SAFE)
    }
}
