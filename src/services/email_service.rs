use std::collections::HashMap;
use tracing::{info, error};
use resend_rs::types::CreateEmailBaseOptions;
use resend_rs::Resend;
use lettre::transport::smtp::authentication::Credentials;
use lettre::transport::smtp::AsyncSmtpTransport;
use lettre::message::{Mailbox, Message, MultiPart, SinglePart};
use lettre::Tokio1Executor;
use lettre::AsyncTransport;
use crate::config::EmailConfig;

#[derive(Debug, Clone)]
pub enum EmailProvider {
    Resend(Resend),
    Smtp(SmtpClient),
}

#[derive(Debug, Clone)]
pub struct SmtpClient {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
}

#[derive(Debug, Clone)]
pub struct EmailService {
    pub provider: EmailProvider,
    pub config: EmailConfig,
    pub templates: EmailTemplateManager,
}

#[derive(Debug, <PERSON>lone)]
pub struct EmailTemplateManager {
    pub templates: HashMap<String, EmailTemplate>,
}

impl Default for EmailTemplateManager {
    fn default() -> Self {
        Self::new()
    }
}

#[derive(Debug, Clone)]
pub struct EmailTemplate {
    pub subject: String,
    pub html_body: String,
    pub text_body: String,
}

#[derive(Debug, thiserror::Error)]
pub enum EmailError {
    #[error("Email provider error: {0}")]
    ProviderError(String),
    #[error("Template not found: {0}")]
    TemplateNotFound(String),
    #[error("Template rendering error: {0}")]
    TemplateRenderError(String),
    #[error("Invalid email address: {0}")]
    InvalidEmailAddress(String),
    #[error("Email sending failed: {0}")]
    SendingFailed(String),
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
}

#[derive(Debug, Clone)]
pub struct EmailContext {
    pub username: String,
    pub verification_url: String,
    pub verification_token: String,
    pub company_name: String,
    pub support_email: String,
}

impl EmailService {
    /// Create a new `EmailService`.
    ///
    /// # Errors
    /// Returns `EmailError::ConfigurationError` if the provider or credentials are invalid.
    pub fn new(config: EmailConfig) -> Result<Self, EmailError> {
        let provider = match config.provider.as_str() {
            "resend" => {
                if config.resend_api_key.is_empty() {
                    return Err(EmailError::ConfigurationError("RESEND API key is required".to_string()));
                }
                let resend = Resend::new(&config.resend_api_key);
                EmailProvider::Resend(resend)
            }
            "smtp" => {
                EmailProvider::Smtp(SmtpClient {
                    host: config.smtp.host.clone(),
                    port: config.smtp.port,
                    username: config.smtp.user.clone(),
                    password: config.smtp.password.clone(),
                })
            }
            _ => return Err(EmailError::ConfigurationError(format!("Unsupported email provider: {}", config.provider))),
        };
        let templates = EmailTemplateManager::new();
        Ok(Self {
            provider,
            config,
            templates,
        })
    }

    /// Send email verification email.
    ///
    /// # Errors
    /// Returns `EmailError` if template rendering or sending fails.
    pub async fn send_verification_email(
        &self,
        to_email: &str,
        username: &str,
        verification_token: &str,
    ) -> Result<(), EmailError> {
        info!("Sending verification email to: {}", to_email);
        let template = self.templates.get_template("email_verification")?;
        let verification_url = format!("{}/auth/verify-email?token={}", self.get_base_url(), verification_token);
        let context = EmailContext {
            username: username.to_string(),
            verification_url,
            verification_token: verification_token.to_string(),
            company_name: "CrabShield".to_string(),
            support_email: self.config.reply_to.clone(),
        };
        let rendered_template = self.templates.render_template(template, &context)?;
        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send password reset email.
    ///
    /// # Errors
    /// Returns `EmailError` if template rendering or sending fails.
    pub async fn send_password_reset_email(
        &self,
        to_email: &str,
        username: &str,
        reset_token: &str,
    ) -> Result<(), EmailError> {
        info!("Sending password reset email to: {}", to_email);
        let template = self.templates.get_template("password_reset")?;
        let reset_url = format!("{}/auth/reset-password?token={}", self.get_base_url(), reset_token);
        let context = EmailContext {
            username: username.to_string(),
            verification_url: reset_url.clone(),
            verification_token: reset_token.to_string(),
            company_name: "CrabShield".to_string(),
            support_email: self.config.reply_to.clone(),
        };
        let rendered_template = self.templates.render_template(template, &context)?;
        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send MFA setup notification email.
    ///
    /// # Errors
    /// Returns `EmailError` if template rendering or sending fails.
    pub async fn send_mfa_setup_email(
        &self,
        to_email: &str,
        username: &str,
    ) -> Result<(), EmailError> {
        info!("Sending MFA setup notification email to: {}", to_email);
        let template = self.templates.get_template("mfa_setup")?;
        let context = EmailContext {
            username: username.to_string(),
            verification_url: String::new(),
            verification_token: String::new(),
            company_name: "CrabShield".to_string(),
            support_email: self.config.reply_to.clone(),
        };
        let rendered_template = self.templates.render_template(template, &context)?;
        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Send OAuth account linking confirmation email.
    ///
    /// # Errors
    /// Returns `EmailError` if template rendering or sending fails.
    pub async fn send_oauth_link_email(
        &self,
        to_email: &str,
        username: &str,
        provider: &str,
    ) -> Result<(), EmailError> {
        info!("Sending OAuth link confirmation email to: {}", to_email);
        let template = self.templates.get_template("oauth_link")?;
        let context = EmailContext {
            username: username.to_string(),
            verification_url: provider.to_string(),
            verification_token: String::new(),
            company_name: "CrabShield".to_string(),
            support_email: self.config.reply_to.clone(),
        };
        let rendered_template = self.templates.render_template(template, &context)?;
        self.send_email(
            to_email,
            &rendered_template.subject,
            &rendered_template.html_body,
            Some(&rendered_template.text_body),
        ).await
    }

    /// Generic email sending method.
    ///
    /// # Errors
    /// Returns `EmailError` if sending fails.
    pub async fn send_email(
        &self,
        to_email: &str,
        subject: &str,
        html_body: &str,
        text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        match &self.provider {
            EmailProvider::Resend(resend) => {
                if let Err(e) = self.send_via_resend(resend, to_email, subject, html_body, text_body).await {
                    error!("Resend failed: {e}, falling back to SMTP");
                    if let EmailProvider::Smtp(smtp) = &self.provider {
                        self.send_via_smtp(smtp, to_email, subject, html_body, text_body).await
                    } else {
                        Err(e)
                    }
                } else {
                    Ok(())
                }
            }
            EmailProvider::Smtp(smtp) => {
                self.send_via_smtp(smtp, to_email, subject, html_body, text_body).await
            }
        }
    }

    /// Send via Resend provider.
    ///
    /// # Errors
    /// Returns `EmailError` if sending fails.
    pub async fn send_via_resend(
        &self,
        resend: &Resend,
        to_email: &str,
        subject: &str,
        html_body: &str,
        text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        let from = self.config.from_address.clone();
        let to = vec![to_email.to_string()];
        let mut email = CreateEmailBaseOptions::new(&from, to, subject)
            .with_html(html_body);
        if let Some(text) = text_body {
            email = email.with_text(text);
        }
        resend.emails.send(email)
            .await
            .map_err(|e| EmailError::ProviderError(format!("Resend error: {e}")))?;
        Ok(())
    }

    /// Send via SMTP as fallback if the RESEND request fails.
    ///
    /// # Errors
    /// Returns `EmailError` if sending fails.
    pub async fn send_via_smtp(
        &self,
        smtp_client: &SmtpClient,
        to_email: &str,
        subject: &str,
        html_body: &str,
        text_body: Option<&str>,
    ) -> Result<(), EmailError> {
        let from: Mailbox = self.config.from_address.parse().map_err(|e| EmailError::InvalidEmailAddress(format!("{e}")))?;
        let to: Mailbox = to_email.parse().map_err(|e| EmailError::InvalidEmailAddress(format!("{e}")))?;
        let email = Message::builder()
            .from(from)
            .to(to)
            .subject(subject)
            .multipart(
                MultiPart::alternative()
                    .singlepart(SinglePart::plain(text_body.unwrap_or("").to_string()))
                    .singlepart(SinglePart::html(html_body.to_string()))
            )
            .map_err(|e| EmailError::TemplateRenderError(format!("{e}")))?;
        let creds = Credentials::new(smtp_client.username.clone(), smtp_client.password.clone());
        let mailer = AsyncSmtpTransport::<Tokio1Executor>::builder_dangerous(&smtp_client.host)
            .port(smtp_client.port)
            .credentials(creds)
            .build();
        mailer.send(email).await.map_err(|e| EmailError::SendingFailed(format!("{e}")))?;
        Ok(())
    }

    /// Returns the base URL for the application.
    #[must_use]
    pub const fn get_base_url(&self) -> &'static str {
        // This should come from configuration
        "http://localhost:3000"
    }
}

impl EmailTemplateManager {
    /// Creates a new [`EmailTemplateManager`] and loads all templates.
    #[must_use]
    pub fn new() -> Self {
        let mut templates = HashMap::new();
        templates.insert("email_verification".to_string(), EmailTemplate {
            subject: "Verify your CrabShield account".to_string(),
            html_body: include_str!("../templates/email_verification.html").to_string(),
            text_body: include_str!("../templates/email_verification.txt").to_string(),
        });
        templates.insert("password_reset".to_string(), EmailTemplate {
            subject: "Reset your CrabShield password".to_string(),
            html_body: include_str!("../templates/password_reset.html").to_string(),
            text_body: include_str!("../templates/password_reset.txt").to_string(),
        });
        templates.insert("mfa_setup".to_string(), EmailTemplate {
            subject: "Multi-factor authentication setup".to_string(),
            html_body: include_str!("../templates/mfa_setup.html").to_string(),
            text_body: include_str!("../templates/mfa_setup.txt").to_string(),
        });
        templates.insert("oauth_link".to_string(), EmailTemplate {
            subject: "Link your OAuth account".to_string(),
            html_body: include_str!("../templates/oauth_link.html").to_string(),
            text_body: include_str!("../templates/oauth_link.txt").to_string(),
        });
        Self { templates }
    }

    /// Gets a template by name.
    ///
    /// # Errors
    /// Returns `EmailError::TemplateNotFound` if the template does not exist.
    pub fn get_template(&self, template_name: &str) -> Result<&EmailTemplate, EmailError> {
        self.templates.get(template_name).ok_or_else(|| EmailError::TemplateNotFound(template_name.to_string()))
    }

    /// Renders a template with the given context.
    ///
    /// # Errors
    /// Returns `EmailError::TemplateRenderError` if rendering fails.
    pub fn render_template(&self, template: &EmailTemplate, context: &EmailContext) -> Result<EmailTemplate, EmailError> {
        let subject = self.render_string(&template.subject, context)?;
        let html_body = self.render_string(&template.html_body, context)?;
        let text_body = self.render_string(&template.text_body, context)?;
        Ok(EmailTemplate { subject, html_body, text_body })
    }

    /// Renders a string template with the given context.
    ///
    /// # Errors
    /// Returns `EmailError::TemplateRenderError` if rendering fails.
    pub fn render_string(&self, template_str: &str, context: &EmailContext) -> Result<String, EmailError> {
        let mut result = template_str.to_string();
        result = result.replace("{{username}}", &context.username);
        result = result.replace("{{verification_url}}", &context.verification_url);
        result = result.replace("{{verification_token}}", &context.verification_token);
        result = result.replace("{{company_name}}", &context.company_name);
        result = result.replace("{{support_email}}", &context.support_email);
        Ok(result)
    }
}
