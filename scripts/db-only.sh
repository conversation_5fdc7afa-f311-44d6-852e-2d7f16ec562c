#!/bin/bash

# Database-only management script for CrabShield Auth Service
# This script helps manage just the database and Redis services without building the application

set -e

COMPOSE_FILE="docker-compose.db-only.yml"

show_help() {
    echo "CrabShield Database Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start the database and Redis services"
    echo "  stop      Stop the database and Redis services"
    echo "  restart   Restart the database and Redis services"
    echo "  logs      Show logs from the database services"
    echo "  status    Show status of the database services"
    echo "  clean     Stop services and remove volumes (WARNING: This will delete all data!)"
    echo "  psql      Connect to the PostgreSQL database"
    echo "  redis     Connect to the Redis instance"
    echo "  help      Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  POSTGRES_PASSWORD  - PostgreSQL password (default: auth_secure_password_123)"
    echo "  REDIS_PASSWORD     - Redis password (default: redis_secure_password_123)"
    echo "  POSTGRES_PORT      - PostgreSQL port (default: 5432)"
    echo "  REDIS_PORT         - Redis port (default: 6379)"
}

start_services() {
    echo "Starting database services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    echo "Database services started!"
    echo ""
    echo "Connection details:"
    echo "  PostgreSQL: postgresql://auth_user:${POSTGRES_PASSWORD:-auth_secure_password_123}@localhost:${POSTGRES_PORT:-5432}/crabshield_auth"
    echo "  Redis: redis://:${REDIS_PASSWORD:-redis_secure_password_123}@localhost:${REDIS_PORT:-6379}"
}

stop_services() {
    echo "Stopping database services..."
    docker-compose -f "$COMPOSE_FILE" down
    echo "Database services stopped!"
}

restart_services() {
    echo "Restarting database services..."
    docker-compose -f "$COMPOSE_FILE" restart
    echo "Database services restarted!"
}

show_logs() {
    docker-compose -f "$COMPOSE_FILE" logs -f
}

show_status() {
    docker-compose -f "$COMPOSE_FILE" ps
}

clean_services() {
    echo "WARNING: This will stop services and remove all data volumes!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Stopping services and removing volumes..."
        docker-compose -f "$COMPOSE_FILE" down -v
        echo "Services stopped and volumes removed!"
    else
        echo "Operation cancelled."
    fi
}

connect_psql() {
    echo "Connecting to PostgreSQL..."
    docker exec -it crabshield-auth-postgres psql -U auth_user -d crabshield_auth
}

connect_redis() {
    echo "Connecting to Redis..."
    docker exec -it crabshield-auth-redis redis-cli -a "${REDIS_PASSWORD:-redis_secure_password_123}"
}

case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_services
        ;;
    psql)
        connect_psql
        ;;
    redis)
        connect_redis
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
