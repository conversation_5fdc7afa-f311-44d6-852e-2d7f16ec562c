# Standalone Dockerfile for VPS deployment
# This builds the auth service as a completely independent service
FROM rust:1.87-bookworm as builder

# No need to install build dependencies as they're included in bookworm

# Set working directory
WORKDIR /app

# Copy Cargo files for dependency caching
COPY Cargo.toml Cargo.lock ./

# Copy source code
COPY src ./src
COPY migrations ./migrations

# Build the application
RUN cargo build --release

# Runtime stage - Use the same bookworm base for consistency
FROM debian:bookworm-slim

# Install runtime dependencies (minimal set needed for production)
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libpq5 \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Create app user for security
RUN useradd -m -u 1001 authservice

# Set working directory
WORKDIR /app

# Copy binary and migrations from builder stage
COPY --from=builder /app/target/release/crabshield /usr/local/bin/crabshield
COPY --from=builder /app/migrations ./migrations

# Set permissions
RUN chown -R authservice:authservice /app && \
    chmod +x /usr/local/bin/crabshield

# Switch to app user
USER authservice

# Expose port
EXPOSE 8080

# Command to run the application
CMD ["crabshield"]
