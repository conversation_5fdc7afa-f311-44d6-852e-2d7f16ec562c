version: '3.8'

services:
  # PostgreSQL Database for Auth Service
  auth-postgres:
    image: postgres:17.5-alpine
    container_name: crabshield-auth-postgres
    environment:
      POSTGRES_DB: crabshield_auth
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-auth_secure_password_123}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - auth_postgres_data:/var/lib/postgresql/data
      - ./docker/postgres-init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - auth-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d crabshield_auth"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for Session Storage and Caching
  auth-redis:
    image: redis:7-alpine
    container_name: crabshield-auth-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_secure_password_123} --appendonly yes
    volumes:
      - auth_redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - auth-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

networks:
  auth-network:
    driver: bridge
    name: crabshield-auth-network

volumes:
  auth_postgres_data:
    name: crabshield-auth-postgres-data
  auth_redis_data:
    name: crabshield-auth-redis-data
